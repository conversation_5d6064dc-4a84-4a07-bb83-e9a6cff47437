
<div class="form-template">
    <form wire:submit.prevent="update">
        @csrf


        @php
            $steps = ['Entreprise', 'Location', 'Description', 'Images'];
            $icons = ['fa-briefcase', 'fa-map-marker-alt', 'fa-info-circle', 'fa-image'];
        @endphp
        <x-admin.form-stepper :steps="$steps" :currentStep="$currentStep" :icons="$icons" :nextDisabled="false" />

        <!-- Step 1: Entreprise -->
            <div class="step-content {{ $currentStep == 0 ? '' : 'd-none' }}">
                <div class="row align-items-start">
                        @include('admin.annonce.entreprise-template', [
                            'entreprises' => $entreprises,
                        ])
                        <div class="col-md-4 col-xs-12 is-active p-0">
                                <h3 class="required">Statut</h3>
                                <select class="form-control" name="is_active" wire:model.defer='is_active' required>
                                    <option value="1">Actif</option>
                                    <option value="0">Inactif</option>
                                </select>
                                @error('is_active')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                        </div>
                </div>
                <x-admin.step-navigation :currentStep="$currentStep" :lastStep="3" />

            </div>


            <!-- Step 2: Location -->
            <div class="step-content {{ $currentStep == 1 ? '' : 'd-none' }}">
                    <div class="row align-items-start">
                        @include('admin.annonce.location-template', [
                            'pays' => $pays,
                            'villes' => $villes,
                            'quartiers' => $quartiers,
                        ])
                    </div>
                
                <x-admin.step-navigation :currentStep="$currentStep" :lastStep="3" />
            </div>

        <!-- Step 2: Description -->
        <div class="step-content {{ $currentStep == 2 ? '' : 'd-none' }}">
            <div class="row align-items-start">
                @include('admin.annonce.reference-select-component', [
                    'title' => 'Type de musique',
                    'name' => 'types_musique',
                    'options' => $list_types_musique,
                ])

                @include('admin.annonce.reference-select-component', [
                    'title' => 'Equipements nocturnes',
                    'name' => 'equipements_vie_nocturne',
                    'options' => $list_equipements_vie_nocturne,
                ])

                @include('admin.annonce.reference-select-component', [
                    'title' => 'Commodités',
                    'name' => 'commodites',
                    'options' => $list_commodites,
                ])
            </div>
            <div class="row align-items-start">

                {{-- @include('admin.annonce.reference-select-component', [
                    'title' => 'Services proposés',
                    'name' => 'services',
                    'options' => $list_services,
                ]) --}}
                @include('admin.annonce.description-component')
            </div>
            <x-admin.step-navigation :currentStep="$currentStep" :lastStep="3" />
        </div>

        <!-- Step 3: Images -->
        <div class="step-content {{ $currentStep == 3 ? '' : 'd-none' }}">
            <div class="row align-items-start">
                @include('admin.annonce.edit-galery-component', [
                    'galerie' => $galerie,
                    'old_galerie' => $old_galerie,
                ])
            </div>
            <div class="row padd-bot-15 {{ $currentStep == 3 ? '' : 'd-none' }}">
                 @include('admin.annonce.edit-validation-buttons')
            </div>
            <!-- @include('admin.annonce.create-validation-buttons') -->
        </div>
    </form>
</div>


@push('scripts')
    <script>
        $(document).ready(function() {
            $('#submit-btn').click(function() {
                var description = $('.ql-editor').html();
                @this.set('description', description);
            });



        });
    </script>
@endpush
