<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Migration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .map-container {
            margin: 20px 0;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
        }
        .map {
            width: 100%;
            height: 400px;
        }
        .controls {
            padding: 10px;
            background: #f5f5f5;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>Google Maps AdvancedMarkerElement Migration Test</h1>
    
    <div class="info">
        <strong>Test Instructions:</strong>
        <ul>
            <li>Click on the map to place markers</li>
            <li>Use the "Locate Me" button to get your current location</li>
            <li>Check browser console for any deprecation warnings</li>
            <li>Verify that markers appear and function correctly</li>
        </ul>
    </div>

    <!-- Test Map 1: Using maps-scripts component -->
    <div class="map-container">
        <div class="controls">
            <h3>Test 1: Maps Scripts Component</h3>
            <button onclick="locateMe()">Locate Me</button>
            <button onclick="testSetMarker()">Set Test Marker</button>
        </div>
        <div id="test-map-1" class="map"></div>
    </div>

    <!-- Test Map 2: Using mapHelper directly -->
    <div class="map-container">
        <div class="controls">
            <h3>Test 2: MapHelper Direct Usage</h3>
            <button onclick="testMapHelper()">Initialize MapHelper</button>
            <button onclick="testMapHelperMarker()">Set MapHelper Marker</button>
        </div>
        <div id="test-map-2" class="map"></div>
    </div>

    <!-- Test Map 3: Basic admin map -->
    <div class="map-container">
        <div class="controls">
            <h3>Test 3: Basic Admin Map Component</h3>
            <p>Click on map to place markers</p>
        </div>
        <div id="test-map-3" class="map"></div>
    </div>

    <!-- Include Google Maps API -->
    <script src="{{ asset('assets_client/js/google-maps-helper.js') }}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initGoogleMapsGlobal" async defer></script>

    <!-- Test Map 1: Using maps-scripts component -->
    <x-public.maps-scripts
        :latitude="8.6195"
        :longitude="0.8248"
        mapId="test-map-1"
        :editable="true"
        :zoom="13"
    />

    <!-- Test Map 3: Basic admin map -->
    <x-admin.basic-map-scripts
        :center="['lat' => 8.6195, 'lng' => 0.8248]"
        mapId="test-map-3"
        :clickable="true"
        :zoom="13"
    />

    <script>
        // Test functions
        function testSetMarker() {
            // Test setting a marker at a specific location
            if (typeof placeMarker === 'function') {
                placeMarker({ lat: 8.6195, lng: 0.8248 });
                console.log('Test marker placed using placeMarker function');
            } else {
                console.error('placeMarker function not available');
            }
        }

        function testMapHelper() {
            if (typeof mapHelper !== 'undefined' && mapHelper) {
                mapHelper.initMap('test-map-2', {
                    zoom: 13,
                    center: { lat: 8.6195, lng: 0.8248 }
                });
                
                mapHelper.addClickListener((coords) => {
                    console.log('MapHelper click:', coords);
                });
                
                console.log('MapHelper initialized for test-map-2');
            } else {
                console.error('mapHelper not available');
            }
        }

        function testMapHelperMarker() {
            if (typeof mapHelper !== 'undefined' && mapHelper) {
                mapHelper.setMarker(8.6195, 0.8248, 15);
                console.log('MapHelper marker set');
            } else {
                console.error('mapHelper not available');
            }
        }

        // Listen for location events
        window.addEventListener('locationFound', (event) => {
            console.log('Location found event:', event.detail);
        });

        window.addEventListener('basicMapLocationSet', (event) => {
            console.log('Basic map location set event:', event.detail);
        });

        // Check for deprecation warnings
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            if (args.some(arg => typeof arg === 'string' && arg.includes('google.maps.Marker'))) {
                console.error('🚨 DEPRECATION WARNING DETECTED:', ...args);
                alert('Deprecation warning detected! Check console for details.');
            }
            originalConsoleWarn.apply(console, args);
        };

        console.log('Google Maps Migration Test Page Loaded');
        console.log('Check for any deprecation warnings in the console');
    </script>
</body>
</html>
