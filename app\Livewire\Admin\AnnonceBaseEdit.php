<?php

namespace App\Livewire\Admin;

trait AnnonceBaseEdit
{
        // Add stepper functionality to the base trait
    public $currentStep = 0;
    
    // Step navigation methods
    public function nextStep()
    {
        \Log::info("Current step: {$this->currentStep}");
        // Validate current step before proceeding
        $this->validateCurrentStep();
        
        // If validation passes, move to next step
        $this->currentStep++;
    }

    public function previousStep()
    {
        if ($this->currentStep > 0) {
            $this->currentStep--;
        }
    }
    
    // This method should be overridden in child classes to provide step-specific validation
    protected function validateCurrentStep()
    {
        // Default implementation - can be overridden in child classes
        if ($this->currentStep == 0) {
            $this->validate([
                'entreprise_id' => 'required|exists:entreprises,id',
                'nom' => 'required|string|min:3',
            ]);
        }
    }

    public $old_galerie = [];

    public $deleted_old_galerie = [];

    public $is_old_galerie = true;

    public $old_image;

    public $selected_images = [];

    public $galerie = [];

    public $image;

    public function updatedSelectedImages($images)
    {
        foreach ($images as $image) {
            $this->galerie[] = $image;
        }

        $this->selected_images = [];
    }

    public function removeImage($array_name, $index)
    {
        if ($array_name == 'old_galerie') {
            $this->deleted_old_galerie[] = $index; // correspond à l'id de l'image dans la base de données
        } elseif ($array_name == 'galerie') {
            unset($this->galerie[$index]);
            $this->galerie = array_values($this->galerie); // Réindexer le tableau après suppression
        }
    }

    public function removeAllImages()
    {
        $this->galerie = [];
        $this->deleted_old_galerie = [];
        foreach ($this->old_galerie as $image) {
            $this->deleted_old_galerie[] = $image->id;
        }
    }

    // Cancel all modifications
    public function restoreImages()
    {
        $this->galerie = [];
        $this->deleted_old_galerie = [];
    }
}
