@props([
    'center' => ['lat' => 8.6195, 'lng' => 0.8248],
    'zoom' => 13,
    'clickable' => true,
    'mapId' => 'map'
])

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    let map, marker;

    function initBasicMap() {
        // Initialize Google Map with AdvancedMarkerElement support
        map = new google.maps.Map(document.getElementById('{{ $mapId }}'), {
            zoom: {{ $zoom }},
            center: {
                lat: {{ $center['lat'] }},
                lng: {{ $center['lng'] }}
            },
            mapId: "DEMO_MAP_ID" // Required for AdvancedMarkerElement
        });

        // Initialize mapHelper if available
        if (typeof mapHelper !== 'undefined' && mapHelper) {
            mapHelper.map = map;
        }

        @if($clickable)
        // Allow map click to set marker
        map.addListener("click", function(e) {
            placeBasicMarker(e.latLng);
        });
        @endif
    }

    // Place or move marker
    function placeBasicMarker(location) {
        if (marker) {
            marker.position = location;
        } else {
            marker = new google.maps.marker.AdvancedMarkerElement({
                position: location,
                map: map
            });
        }

        // Update mapHelper reference if available
        if (typeof mapHelper !== 'undefined' && mapHelper) {
            mapHelper.marker = marker;
        }

        // Get coordinates with proper precision
        const lat = typeof location.lat === 'function'
            ? parseFloat(location.lat()).toFixed(8)
            : parseFloat(location.lat).toFixed(8);
        const lng = typeof location.lng === 'function'
            ? parseFloat(location.lng()).toFixed(8)
            : parseFloat(location.lng).toFixed(8);

        console.log('Basic map coordinates updated:', { latitude: lat, longitude: lng });
        
        // Dispatch custom event for other components to listen
        window.dispatchEvent(new CustomEvent('basicMapLocationSet', {
            detail: { lat: parseFloat(lat), lng: parseFloat(lng) }
        }));
    }

    // Initialize when Google script loads
    if (typeof google !== 'undefined') {
        initBasicMap();
    } else {
        console.error("Google Maps API not loaded for basic map");
    }

    // Make functions globally available
    window.placeBasicMarker = placeBasicMarker;
    window.basicMap = map;
});
</script>
@endpush
