<div class="form-template">
    <form wire:submit.prevent="update">
        @csrf


            <div class="card-body pt-4">
                @php
                    $steps = ['Entreprise', 'Location', 'Propriété', 'Caractéristiques', 'Images'];
                    $icons = ['fa-building', 'fa-map-marker-alt', 'fa-home', 'fa-list', 'fa-images'];
                @endphp
                
                <x-admin.form-stepper :steps="$steps" :currentStep="$currentStep" :icons="$icons" :nextDisabled="false" />

                <!-- Step 1: Entreprise -->
                <div class="step-content {{ $currentStep == 0 ? '' : 'd-none' }}">
                        <div class="paddingLeft row align-items-start">
                            @include('admin.annonce.entreprise-template', [
                                'entreprises' => $entreprises,
                            ])
                    <div class="col-md-4 col-xs-12 is-active p-0">
                    <div class="col">
                        <h3 class="required">Statut</h3>
                        <h4>Indiquez si l'annonce est active ou inactive</h4>
                        <select class="form-control" name="is_active" wire:model.defer='is_active' required>
                            <option value="1">Actif</option>
                            <option value="0">Inactif</option>
                        </select>
                        @error('is_active')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                        </div>
                    
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
                </div>

                <!-- Step 2: Location -->
                <div class="step-content {{ $currentStep == 1 ? '' : 'd-none' }}">
                        <div class="paddingLeft row align-items-start">
                            @include('admin.annonce.location-template', [
                                'pays' => $pays,
                                'villes' => $villes,
                                'quartiers' => $quartiers,
                                'longitude' => $longitude,
                                'latitude' => $latitude,
                            ])
                        </div>
                    
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
                </div>

                <!-- Step 3: Propriété -->
                <div class="step-content {{ $currentStep == 2 ? '' : 'd-none' }}">
                    <div class="paddingLeft row align-items-start">
                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3 class="required">Nombre de chambre</h3>
                                <h4>Indiquez le nombre de chambre</h4>
                                <input class="form-control" name="nombre_chambre" type="number" placeholder="" wire:model.defer='nombre_chambre' required>
                                @error('nombre_chambre')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3>Nombre de personnes</h3>
                                <h4>Indiquez le nombre de personnes</h4>
                                <input class="form-control" name="nombre_personne" type="number" placeholder="" wire:model.defer='nombre_personne'>
                                @error('nombre_personne')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3>Nombre de salle de bain</h3>
                                <h4>Indiquez le nombre de salle de bain</h4>
                                <input class="form-control" name="nombre_salles_bain" type="number" placeholder="" wire:model.defer='nombre_salles_bain'>
                                @error('nombre_salles_bain')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="paddingLeft row align-items-start mt-3">
                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3>Superficie</h3>
                                <h4>Indiquez la superficie en m²</h4>
                                <input class="form-control" name="superficie" type="number" placeholder="" wire:model.defer='superficie'>
                                @error('superficie')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 min-price p-0">
                            <div class="col">
                                <h3>Prix minimum</h3>
                                <h4>Indiquez le prix minimum</h4>
                                <input class="form-control" name="prix_min" type="number" placeholder="" wire:model='prix_min'>
                                @error('prix_min')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 max-price p-0">
                            <div class="col">
                                <h3>Prix maximum</h3>
                                <h4>Indiquez le prix maximum</h4>
                                <input class="form-control" name="prix_max" type="number" placeholder="" wire:model='prix_max'>
                                @error('prix_max')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="paddingLeft row align-items-start mt-3">
                        <div class="col-12">
                            <h3>Description</h3>
                            <textarea class="form-control" wire:model.defer="description" rows="5"></textarea>
                            @error('description')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
                </div>

                <!-- Step 4: Caractéristiques -->
                <div class="step-content {{ $currentStep == 3 ? '' : 'd-none' }}">
                    <div class="paddingLeft row align-items-start">
                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Type d\'hebergement',
                            'name' => 'types_hebergement',
                            'options' => $list_types_hebergement,
                        ])

                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Type de lit',
                            'name' => 'types_lit',
                            'options' => $list_types_lit,
                            'required' => true,
                        ])

                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Commodités',
                            'name' => 'commodites',
                            'options' => $list_commodites,
                        ])
                    </div>

                    <div class="paddingLeft row align-items-start mt-3">
                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Services proposés',
                            'name' => 'services',
                            'options' => $list_services,
                        ])

                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Equipements d\'hébergement',
                            'name' => 'equipements_herbegement',
                            'options' => $list_equipements_herbegement,
                        ])

                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Equipements de cuisine',
                            'name' => 'equipements_cuisine',
                            'options' => $list_equipements_cuisine,
                            'required' => true,
                        ])
                    </div>

                    <div class="paddingLeft row align-items-start mt-3">
                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Equipements de salle de bain',
                            'name' => 'equipements_salle_bain',
                            'options' => $list_equipements_salle_bain,
                        ])
                    </div>
                    
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
                </div>

                <!-- Step 5: Images -->
                <div class="step-content {{ $currentStep == 4 ? '' : 'd-none' }}"> 
                    <div class="paddingLeft row">
                        @include('admin.annonce.edit-galery-component', [
                            'galerie' => $galerie,
                            'old_galerie' => $old_galerie,
                        ])
                    </div>
                    <div class="paddingLeft row d-flex justify-content-between my-4 {{ $currentStep == 4 ? '' : 'd-none' }}">
                        @include('admin.annonce.edit-validation-buttons')
                    </div>
                </div>
            </div>
    </form>
</div>

@push('scripts')
    <script>
        $(document).ready(function() {
            $('#submit-btn').click(function() {
                var description = $('.ql-editor').html();
            });




        });
    </script>
@endpush
