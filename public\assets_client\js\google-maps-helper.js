/**
 * Google Maps Helper with AdvancedMarkerElement
 * Provides utility functions for Google Maps integration
 */

// Global map helper object
let mapHelper = null;

// Global callback for Google Maps API initialization
function initGoogleMapsGlobal() {
    console.log('Google Maps API loaded successfully');
    
    // Initialize mapHelper if needed
    if (!mapHelper) {
        mapHelper = new GoogleMapsHelper();
    }
}

class GoogleMapsHelper {
    constructor() {
        this.map = null;
        this.marker = null;
        this.defaultCenter = { lat: 8.6195, lng: 0.8248 }; // Default to Togo coordinates
    }

    /**
     * Initialize a map with given options
     */
    initMap(elementId, options = {}) {
        const defaultOptions = {
            zoom: 13,
            center: this.defaultCenter,
            mapId: "DEMO_MAP_ID" // Required for AdvancedMarkerElement
        };

        const mapOptions = { ...defaultOptions, ...options };
        
        this.map = new google.maps.Map(document.getElementById(elementId), mapOptions);
        
        return this.map;
    }

    /**
     * Set a marker at the given coordinates
     */
    setMarker(lat, lng, zoom = null) {
        if (!this.map) {
            console.error('Map not initialized. Call initMap() first.');
            return null;
        }

        const position = { lat: parseFloat(lat), lng: parseFloat(lng) };

        // Remove existing marker
        if (this.marker) {
            this.marker.map = null;
        }

        // Create new marker using AdvancedMarkerElement
        this.marker = new google.maps.marker.AdvancedMarkerElement({
            position: position,
            map: this.map,
            title: 'Location'
        });

        // Center map on marker
        this.map.setCenter(position);
        
        if (zoom !== null) {
            this.map.setZoom(zoom);
        }

        return this.marker;
    }

    /**
     * Get current location and set marker
     */
    locateMe(zoom = 13) {
        if (!navigator.geolocation) {
            console.error('Geolocation is not supported by this browser.');
            return;
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                this.setMarker(lat, lng, zoom);
                
                // Trigger custom event for location found
                window.dispatchEvent(new CustomEvent('locationFound', {
                    detail: { lat, lng }
                }));
            },
            (error) => {
                console.error('Error getting location:', error.message);
                alert('Impossible de récupérer votre position.');
            }
        );
    }

    /**
     * Add click listener to map
     */
    addClickListener(callback) {
        if (!this.map) {
            console.error('Map not initialized. Call initMap() first.');
            return;
        }

        this.map.addListener('click', (event) => {
            const lat = event.latLng.lat();
            const lng = event.latLng.lng();
            
            // Set marker at clicked location
            this.setMarker(lat, lng);
            
            // Call callback if provided
            if (callback && typeof callback === 'function') {
                callback({ lat, lng });
            }
        });
    }

    /**
     * Remove current marker
     */
    removeMarker() {
        if (this.marker) {
            this.marker.map = null;
            this.marker = null;
        }
    }

    /**
     * Get current marker position
     */
    getMarkerPosition() {
        if (!this.marker) {
            return null;
        }
        
        return this.marker.position;
    }

    /**
     * Create a marker with custom content
     */
    createCustomMarker(lat, lng, content, title = '') {
        const position = { lat: parseFloat(lat), lng: parseFloat(lng) };
        
        // Create marker content element
        let markerContent;
        if (typeof content === 'string') {
            markerContent = document.createElement('div');
            markerContent.innerHTML = content;
        } else {
            markerContent = content;
        }

        return new google.maps.marker.AdvancedMarkerElement({
            position: position,
            content: markerContent,
            title: title
        });
    }

    /**
     * Create a marker with custom icon
     */
    createIconMarker(lat, lng, iconUrl, size = { width: 32, height: 32 }, title = '') {
        const markerContent = document.createElement('div');
        markerContent.style.backgroundImage = `url(${iconUrl})`;
        markerContent.style.backgroundSize = 'contain';
        markerContent.style.backgroundRepeat = 'no-repeat';
        markerContent.style.width = `${size.width}px`;
        markerContent.style.height = `${size.height}px`;
        markerContent.title = title;

        return this.createCustomMarker(lat, lng, markerContent, title);
    }
}

// Initialize mapHelper when script loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Google Maps API to be available
    if (typeof google !== 'undefined' && google.maps) {
        if (!mapHelper) {
            mapHelper = new GoogleMapsHelper();
        }
    }
});
